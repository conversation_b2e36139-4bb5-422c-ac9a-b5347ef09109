  18:54:00.609  [MCS:MCS_Server_Initialization]: Starting initialization...  -  Server - MCS_Utils:65
  18:54:00.609  [MCS:PermissionService]: Initialized  -  Server - MCS_Utils:65
  18:54:00.609  [MCS:MiddlewareSystem]: Initializing middleware system...  -  Server - MCS_Utils:65
  18:54:00.609  [MCS:MiddlewareSystem]: Creating fallback middleware implementations...  -  Server - MCS_Utils:65
  18:54:00.609  [MCS:MiddlewareSystem]: Fallback middleware created successfully  -  Server - MCS_Utils:65
  18:54:00.609  [MCS:MiddlewareSystem]: Loading middleware chain...  -  Server - MCS_Utils:65
  18:54:00.609  [MCS:Authentication]: Authentication middleware initialized successfully  -  Server - MCS_Utils:65
  18:54:00.609  [MCS:MiddlewareSystem]: Authentication middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:00.609  [MCS:MiddlewareSystem]: LoadMiddleware: Authentication took 0.373 ms  -  Server - MCS_Utils:65
  18:54:00.609  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Authentication took 0.437 ms  -  Server - MCS_Utils:65
  18:54:00.610  [MCS:Permission]: Permission middleware initialized successfully  -  Server - MCS_Utils:65
  18:54:00.610  [MCS:MiddlewareSystem]: Permission middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:00.610  [MCS:MiddlewareSystem]: LoadMiddleware: Permission took 0.278 ms  -  Server - MCS_Utils:65
  18:54:00.610  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Permission took 0.327 ms  -  Server - MCS_Utils:65
  18:54:00.610  [MCS:RateLimiter]: RateLimiterInit took 0.006 ms  -  Server - MCS_Utils:65
  18:54:00.610  [MCS:RateLimiter]: PerformanceMonitor: RateLimiterInit took 0.054 ms  -  Server - MCS_Utils:65
  18:54:00.610  [MCS:RateLimiter]: Rate limiter middleware initialized successfully  -  Server - MCS_Utils:65
  18:54:00.610  [MCS:MiddlewareSystem]: RateLimiter middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:00.611  [MCS:MiddlewareSystem]: LoadMiddleware: RateLimiter took 0.520 ms  -  Server - MCS_Utils:65
  18:54:00.611  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: RateLimiter took 0.592 ms  -  Server - MCS_Utils:65
  18:54:00.611  [MCS:Logger]: Logger initialized successfully  -  Server - MCS_Utils:65
  18:54:00.611  [MCS:MiddlewareSystem]: Logger middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:00.611  [MCS:MiddlewareSystem]: LoadMiddleware: Logger took 0.421 ms  -  Server - MCS_Utils:65
  18:54:00.611  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Logger took 0.518 ms  -  Server - MCS_Utils:65
  18:54:00.611  [MCS:Analytics]: Analytics initialized successfully  -  Server - MCS_Utils:65
  18:54:00.611  [MCS:MiddlewareSystem]: Analytics middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:MiddlewareSystem]: LoadMiddleware: Analytics took 0.346 ms  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Analytics took 0.394 ms  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:InputValidation]: InputValidation middleware initialized successfully  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:MiddlewareSystem]: InputValidation middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:MiddlewareSystem]: LoadMiddleware: InputValidation took 0.180 ms  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: InputValidation took 0.239 ms  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:MiddlewareSystem]: Middleware chain loaded with 6 modules  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:MiddlewareSystem]: LoadMiddlewareChain took 3.089 ms  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddlewareChain took 3.130 ms  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:MiddlewareSystem]: Middleware system initialized successfully with 6 modules  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:MiddlewareSystem]: MiddlewareInit took 3.510 ms  -  Server - MCS_Utils:65
  18:54:00.612  [MCS:MiddlewareSystem]: PerformanceMonitor: MiddlewareInit took 3.596 ms  -  Server - MCS_Utils:65
  18:54:00.613  [MCS:CommandDispatcher]: Registered command: ban from Ban  -  Server - MCS_Utils:65
  18:54:00.613  [MCS:CommandDispatcher]: Registered command: reload from Reload  -  Server - MCS_Utils:65
  18:54:00.613  [MCS:CommandDispatcher]: Registered alias: sysres -> reload  -  Server - MCS_Utils:65
  18:54:00.613  [MCS:CommandDispatcher]: Registered command: grantperm from GrantPerm  -  Server - MCS_Utils:65
  18:54:00.613  [MCS:CommandDispatcher]: Registered command: revokeperm from RevokePerm  -  Server - MCS_Utils:65
  18:54:00.613  [MCS:MCS_Remotes]: Remotes system initialized successfully  -  Server - MCS_Utils:65
  18:54:00.613  [MCS:MCS_Server_Initialization]: Initialization completed.  -  Server - MCS_Utils:65
  18:54:00.808  [MCS:MCS_CommandParser]: Module initialized  -  Client - MCS_Utils:65
  18:54:00.809  [MCS:MCS_AutocompleteService]: Module initialized  -  Client - MCS_Utils:65
  18:54:01.298  Failed to load sound rbxassetid://1664205910: User is not authorized to access Asset.  -  Studio
  18:54:01.299  The experience doesn't have access permission to use asset id 1664205910. Click to share access  -  Studio
  18:54:01.498  Failed to load sound rbxassetid://1664205910: User is not authorized to access Asset.  -  Studio
  18:54:01.498  The experience doesn't have access permission to use asset id 1664205910. Click to share access  -  Studio
  18:54:05.169  [MCS:MiddlewareSystem]: Middleware system already initialized  -  Server - MCS_Utils:65
  18:54:05.202  [CGS::LocalCharacter] Initializing Ragdoll local character script  -  Client - Ragdoll_Utils:38
  18:54:05.505  [MCS:Console]: Initializing...  -  Client - MCS_Utils:65
  18:54:05.568  [MCS:FeedbackDisplay]: Initializing...  -  Client - MCS_Utils:65
  18:54:05.568  [MCS:MCS_AutocompleteService]: Initializing service  -  Client - MCS_Utils:65
  18:54:05.569  [MCS:MCS_AutocompleteService]: Service initialized successfully  -  Client - MCS_Utils:65
  18:54:05.569  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  18:54:05.569  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:54:05.569  [MCS:MCS_Client_Initialization]: MCS Client initialized  -  Client - MCS_Utils:65
  18:54:05.592  [MCS:MCS_Remotes]: Returned 5 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  18:54:05.618  [MCS:MCS_AutocompleteService]: Fetched 5 available commands  -  Client - MCS_Utils:65
  18:54:05.618  [MCS:AutocompleteService]: getAvailableCommands took 49.108 ms  -  Client - MCS_Utils:65
  18:54:05.618  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 49.245 ms  -  Client - MCS_Utils:65
  18:54:05.618  [MCS:MCS_AutocompleteService]: Returning 5 suggestions for prefix  -  Client - MCS_Utils:65
  18:54:05.618  [MCS:AutocompleteService]: getSuggestions took 49.546 ms  -  Client - MCS_Utils:65
  18:54:05.618  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 49.630 ms  -  Client - MCS_Utils:65
  18:54:06.513  [MCS:Console]: Console toggled via F2  -  Client - MCS_Utils:65
  18:54:07.130  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  18:54:07.130  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:54:07.161  [MCS:MCS_Remotes]: Returned 5 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  18:54:07.194  [MCS:MCS_AutocompleteService]: Fetched 5 available commands  -  Client - MCS_Utils:65
  18:54:07.194  [MCS:AutocompleteService]: getAvailableCommands took 64.416 ms  -  Client - MCS_Utils:65
  18:54:07.194  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 64.590 ms  -  Client - MCS_Utils:65
  18:54:07.194  [MCS:MCS_AutocompleteService]: Returning 5 suggestions for prefix  -  Client - MCS_Utils:65
  18:54:07.194  [MCS:AutocompleteService]: getSuggestions took 64.780 ms  -  Client - MCS_Utils:65
  18:54:07.195  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 64.917 ms  -  Client - MCS_Utils:65
  18:54:09.351  [MCS:MCS_AutocompleteService]: Getting suggestions for: !s  -  Client - MCS_Utils:65
  18:54:09.377  ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Server - MCS_Utils:187
  18:54:09.377  Stack Begin  -  Studio
  18:54:09.377  Script 'ReplicatedStorage.MCS.Shared.MCS_Utils', Line 187 - function stripPrefix  -  Studio - MCS_Utils:187
  18:54:09.378  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 225 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:225
  18:54:09.378  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 367  -  Studio - MCS_Remotes:367
  18:54:09.378  Stack End  -  Studio
  18:54:09.411  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Client - MCS_Utils:65
  18:54:09.412  [MCS:AutocompleteService]: getSuggestions took 60.641 ms  -  Client - MCS_Utils:65
  18:54:09.412  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 60.798 ms  -  Client - MCS_Utils:65
  18:54:09.714  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sy  -  Client - MCS_Utils:65
  18:54:09.744  ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Server - MCS_Utils:187
  18:54:09.744  Stack Begin  -  Studio
  18:54:09.744  Script 'ReplicatedStorage.MCS.Shared.MCS_Utils', Line 187 - function stripPrefix  -  Studio - MCS_Utils:187
  18:54:09.744  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 225 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:225
  18:54:09.744  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 367  -  Studio - MCS_Remotes:367
  18:54:09.744  Stack End  -  Studio
  18:54:09.778  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Client - MCS_Utils:65
  18:54:09.778  [MCS:AutocompleteService]: getSuggestions took 63.915 ms  -  Client - MCS_Utils:65
  18:54:09.778  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 64.063 ms  -  Client - MCS_Utils:65
  18:54:10.060  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sys  -  Client - MCS_Utils:65
  18:54:10.077  ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Server - MCS_Utils:187
  18:54:10.077  Stack Begin  -  Studio
  18:54:10.078  Script 'ReplicatedStorage.MCS.Shared.MCS_Utils', Line 187 - function stripPrefix  -  Studio - MCS_Utils:187
  18:54:10.078  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 225 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:225
  18:54:10.078  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 367  -  Studio - MCS_Remotes:367
  18:54:10.078  Stack End  -  Studio
  18:54:10.094  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Client - MCS_Utils:65
  18:54:10.095  [MCS:AutocompleteService]: getSuggestions took 34.637 ms  -  Client - MCS_Utils:65
  18:54:10.095  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 34.673 ms  -  Client - MCS_Utils:65
  18:54:10.296  [MCS:MCS_AutocompleteService]: Getting suggestions for: !syst  -  Client - MCS_Utils:65
  18:54:10.327  ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Server - MCS_Utils:187
  18:54:10.328  Stack Begin  -  Studio
  18:54:10.328  Script 'ReplicatedStorage.MCS.Shared.MCS_Utils', Line 187 - function stripPrefix  -  Studio - MCS_Utils:187
  18:54:10.328  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 225 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:225
  18:54:10.328  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 367  -  Studio - MCS_Remotes:367
  18:54:10.328  Stack End  -  Studio
  18:54:10.344  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Client - MCS_Utils:65
  18:54:10.344  [MCS:AutocompleteService]: getSuggestions took 48.311 ms  -  Client - MCS_Utils:65
  18:54:10.344  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 48.337 ms  -  Client - MCS_Utils:65
  18:54:10.915  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sys  -  Client - MCS_Utils:65
  18:54:10.944  ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Server - MCS_Utils:187
  18:54:10.945  Stack Begin  -  Studio
  18:54:10.945  Script 'ReplicatedStorage.MCS.Shared.MCS_Utils', Line 187 - function stripPrefix  -  Studio - MCS_Utils:187
  18:54:10.945  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 225 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:225
  18:54:10.945  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 367  -  Studio - MCS_Remotes:367
  18:54:10.945  Stack End  -  Studio
  18:54:10.961  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Client - MCS_Utils:65
  18:54:10.961  [MCS:AutocompleteService]: getSuggestions took 45.770 ms  -  Client - MCS_Utils:65
  18:54:10.961  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 45.891 ms  -  Client - MCS_Utils:65
  18:54:11.131  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sysr  -  Client - MCS_Utils:65
  18:54:11.161  ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Server - MCS_Utils:187
  18:54:11.161  Stack Begin  -  Studio
  18:54:11.161  Script 'ReplicatedStorage.MCS.Shared.MCS_Utils', Line 187 - function stripPrefix  -  Studio - MCS_Utils:187
  18:54:11.161  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 225 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:225
  18:54:11.161  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 367  -  Studio - MCS_Remotes:367
  18:54:11.161  Stack End  -  Studio
  18:54:11.164  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sysre  -  Client - MCS_Utils:65
  18:54:11.178  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Client - MCS_Utils:65
  18:54:11.178  [MCS:AutocompleteService]: getSuggestions took 14.823 ms  -  Client - MCS_Utils:65
  18:54:11.179  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 14.902 ms  -  Client - MCS_Utils:65
  18:54:11.195  ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Server - MCS_Utils:187
  18:54:11.195  Stack Begin  -  Studio
  18:54:11.195  Script 'ReplicatedStorage.MCS.Shared.MCS_Utils', Line 187 - function stripPrefix  -  Studio - MCS_Utils:187
  18:54:11.196  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 225 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:225
  18:54:11.196  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 367  -  Studio - MCS_Remotes:367
  18:54:11.196  Stack End  -  Studio
  18:54:11.211  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Client - MCS_Utils:65
  18:54:11.346  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sysres  -  Client - MCS_Utils:65
  18:54:11.377  ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Server - MCS_Utils:187
  18:54:11.378  Stack Begin  -  Studio
  18:54:11.378  Script 'ReplicatedStorage.MCS.Shared.MCS_Utils', Line 187 - function stripPrefix  -  Studio - MCS_Utils:187
  18:54:11.378  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 225 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:225
  18:54:11.378  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 367  -  Studio - MCS_Remotes:367
  18:54:11.378  Stack End  -  Studio
  18:54:11.394  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Client - MCS_Utils:65
  18:54:11.394  [MCS:AutocompleteService]: getSuggestions took 48.408 ms  -  Client - MCS_Utils:65
  18:54:11.394  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 48.452 ms  -  Client - MCS_Utils:65
  18:54:11.696  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sysres   -  Client - MCS_Utils:65
  18:54:11.727  ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Server - MCS_Utils:187
  18:54:11.728  Stack Begin  -  Studio
  18:54:11.728  Script 'ReplicatedStorage.MCS.Shared.MCS_Utils', Line 187 - function stripPrefix  -  Studio - MCS_Utils:187
  18:54:11.728  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 225 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:225
  18:54:11.728  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 367  -  Studio - MCS_Remotes:367
  18:54:11.728  Stack End  -  Studio
  18:54:11.762  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Client - MCS_Utils:65
  18:54:11.762  [MCS:AutocompleteService]: getSuggestions took 65.565 ms  -  Client - MCS_Utils:65
  18:54:11.762  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 65.818 ms  -  Client - MCS_Utils:65
  18:54:13.531  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sysres  -  Client - MCS_Utils:65
  18:54:13.560  ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Server - MCS_Utils:187
  18:54:13.560  Stack Begin  -  Studio
  18:54:13.561  Script 'ReplicatedStorage.MCS.Shared.MCS_Utils', Line 187 - function stripPrefix  -  Studio - MCS_Utils:187
  18:54:13.561  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 225 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:225
  18:54:13.561  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 367  -  Studio - MCS_Remotes:367
  18:54:13.561  Stack End  -  Studio
  18:54:13.578  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ReplicatedStorage.MCS.Shared.MCS_Utils:187: attempt to concatenate nil with string  -  Client - MCS_Utils:65
  18:54:13.578  [MCS:AutocompleteService]: getSuggestions took 46.278 ms  -  Client - MCS_Utils:65
  18:54:13.578  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 46.425 ms  -  Client - MCS_Utils:65
  18:54:13.797  [MCS:Console]: Command sent: !sysres  -  Client - MCS_Utils:65
  18:54:13.798  [MCS:MCS_Client_Initialization]: Handling command input: !sysres  -  Client - MCS_Utils:65
  18:54:13.798  [MCS:MCS_CommandParser]: Parsing command: !sysres  -  Client - MCS_Utils:65
  18:54:13.798  [MCS:CommandParser]: parse took 0.007 ms  -  Client - MCS_Utils:65
  18:54:13.798  [MCS:CommandParser]: PerformanceMonitor: parse took 0.081 ms  -  Client - MCS_Utils:65
  18:54:13.798  [MCS:MCS_Client_Initialization]: Sent command to server: sysres  -  Client - MCS_Utils:65
  18:54:13.798  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  18:54:13.798  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:54:13.827  [MCS:MCS_Remotes]: Returned 5 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  18:54:13.827  [MCS:CommandDispatcher]: Looking for command: sysres  -  Server - MCS_Utils:65
  18:54:13.827  [MCS:CommandDispatcher]: Resolved to: reload  -  Server - MCS_Utils:65
  18:54:13.828  [MCS:MiddlewareSystem]: Running middleware chain for 1XFPANDAFX1 using command: reload  -  Server - MCS_Utils:65
  18:54:13.828  [MCS:Authentication]: CacheCleanup took 0.001 ms  -  Server - MCS_Utils:65
  18:54:13.828  [MCS:Authentication]: PerformanceMonitor: CacheCleanup took 0.113 ms  -  Server - MCS_Utils:65
  18:54:13.828  [MCS:Authentication]: AuthProcess took 0.175 ms  -  Server - MCS_Utils:65
  18:54:13.828  [MCS:Authentication]: PerformanceMonitor: AuthProcess took 0.251 ms  -  Server - MCS_Utils:65
  18:54:13.828  [MCS:Authentication]: Authentication successful for 1XFPANDAFX1 (1703810675) using command 'reload'  -  Server - MCS_Utils:65
  18:54:13.828  [MCS:MiddlewareSystem]: Middleware Authentication executed in 0.43 ms (Success: true)  -  Server - MCS_Utils:65
  18:54:13.828  [MCS:Permission]: Permission granted for 1XFPANDAFX1 (1703810675) using reload  -  Server - MCS_Utils:65
  18:54:13.828  [MCS:MiddlewareSystem]: Middleware Permission executed in 0.04 ms (Success: true)  -  Server - MCS_Utils:65
  18:54:13.828  [MCS:RateLimiter]: RateLimitCheck took 0.006 ms  -  Server - MCS_Utils:65
  18:54:13.828  [MCS:RateLimiter]: PerformanceMonitor: RateLimitCheck took 0.084 ms  -  Server - MCS_Utils:65
  18:54:13.829  [MCS:MiddlewareSystem]: Middleware RateLimiter executed in 0.16 ms (Success: true)  -  Server - MCS_Utils:65
  18:54:13.829  [MCS:Logger]: Player: 1XFPANDAFX1 (1703810675) | Command: reload | Args:   -  Server - MCS_Utils:65
  18:54:13.829  [MCS:MiddlewareSystem]: Middleware Logger executed in 0.08 ms (Success: true)  -  Server - MCS_Utils:65
  18:54:13.829  [MCS:MiddlewareSystem]: Middleware Analytics executed in 0.02 ms (Success: true)  -  Server - MCS_Utils:65
  18:54:13.829  [MCS:MiddlewareSystem]: Middleware InputValidation executed in 0.01 ms (Success: true)  -  Server - MCS_Utils:65
  18:54:13.829  [MCS:MiddlewareSystem]: Middleware chain completed successfully in 1.24 ms  -  Server - MCS_Utils:65
  18:54:13.829  [MCS:MiddlewareSystem]: MiddlewareExecution took 1.565 ms  -  Server - MCS_Utils:65
  18:54:13.829  [MCS:MiddlewareSystem]: PerformanceMonitor: MiddlewareExecution took 1.721 ms  -  Server - MCS_Utils:65
  18:54:13.829  [MCS:ReloadCommand]: Starting system reload...  -  Server - MCS_Utils:65
  18:54:13.829  [MCS:CommandDispatcher]: Registered command: ban from Ban  -  Server - MCS_Utils:65
  18:54:13.829  [MCS:CommandDispatcher]: Registered command: reload from Reload  -  Server - MCS_Utils:65
  18:54:13.830  [MCS:CommandDispatcher]: Registered alias: sysres -> reload  -  Server - MCS_Utils:65
  18:54:13.830  [MCS:CommandDispatcher]: Registered command: grantperm from GrantPerm  -  Server - MCS_Utils:65
  18:54:13.830  [MCS:CommandDispatcher]: Registered command: revokeperm from RevokePerm  -  Server - MCS_Utils:65
  18:54:13.830  [MCS:MiddlewareSystem]: Reloading middleware system...  -  Server - MCS_Utils:65
  18:54:13.830  [MCS:Authentication]: Cleaning up authentication middleware  -  Server - MCS_Utils:65
  18:54:13.830  [MCS:Authentication]: Cleanup completed. Total auth failures: 0  -  Server - MCS_Utils:65
  18:54:13.830  [MCS:Permission]: Cleaning up permission middleware  -  Server - MCS_Utils:65
  18:54:13.830  [MCS:RateLimiter]: Cleaning up rate limiter middleware  -  Server - MCS_Utils:65
  18:54:13.830  [MCS:RateLimiter]: Cleanup completed. Processed 0 commands, 0 violations total  -  Server - MCS_Utils:65
  18:54:13.830  [MCS:Logger]: Cleaning up logger middleware  -  Server - MCS_Utils:65
  18:54:13.830  [MCS:Analytics]: Cleaning up analytics middleware  -  Server - MCS_Utils:65
  18:54:13.861  [MCS:MCS_AutocompleteService]: Fetched 5 available commands  -  Client - MCS_Utils:65
  18:54:13.861  [MCS:AutocompleteService]: getAvailableCommands took 62.574 ms  -  Client - MCS_Utils:65
  18:54:13.861  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 62.729 ms  -  Client - MCS_Utils:65
  18:54:13.861  [MCS:MCS_AutocompleteService]: Returning 5 suggestions for prefix  -  Client - MCS_Utils:65
  18:54:13.861  [MCS:AutocompleteService]: getSuggestions took 63.212 ms  -  Client - MCS_Utils:65
  18:54:13.861  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 63.420 ms  -  Client - MCS_Utils:65
  18:54:15.144  [MCS:Analytics]: Analytics saved successfully  -  Server - MCS_Utils:65
  18:54:15.145  [MCS:InputValidation]: Cleaning up input validation middleware  -  Server - MCS_Utils:65
  18:54:15.145  [MCS:MiddlewareSystem]: Cleaning up middleware system...  -  Server - MCS_Utils:65
  18:54:15.145  [MCS:Authentication]: Cleaning up authentication middleware  -  Server - MCS_Utils:65
  18:54:15.145  [MCS:Authentication]: Cleanup completed. Total auth failures: 0  -  Server - MCS_Utils:65
  18:54:15.145  [MCS:Permission]: Cleaning up permission middleware  -  Server - MCS_Utils:65
  18:54:15.145  [MCS:RateLimiter]: Cleaning up rate limiter middleware  -  Server - MCS_Utils:65
  18:54:15.146  [MCS:RateLimiter]: Cleanup completed. Processed 0 commands, 0 violations total  -  Server - MCS_Utils:65
  18:54:15.146  [MCS:Logger]: Cleaning up logger middleware  -  Server - MCS_Utils:65
  18:54:15.146  [MCS:Analytics]: Cleaning up analytics middleware  -  Server - MCS_Utils:65
  18:54:15.146  [MCS:Analytics]: DataStores not initialized, skipping save  -  Server - MCS_Utils:65
  18:54:15.146  [MCS:InputValidation]: Cleaning up input validation middleware  -  Server - MCS_Utils:65
  18:54:15.146  [MCS:MiddlewareSystem]: Middleware system cleaned up  -  Server - MCS_Utils:65
  18:54:15.146  [MCS:MiddlewareSystem]: Creating fallback middleware implementations...  -  Server - MCS_Utils:65
  18:54:15.146  [MCS:MiddlewareSystem]: Fallback middleware created successfully  -  Server - MCS_Utils:65
  18:54:15.146  [MCS:MiddlewareSystem]: Initializing middleware system...  -  Server - MCS_Utils:65
  18:54:15.146  [MCS:MiddlewareSystem]: Creating fallback middleware implementations...  -  Server - MCS_Utils:65
  18:54:15.146  [MCS:MiddlewareSystem]: Fallback middleware created successfully  -  Server - MCS_Utils:65
  18:54:15.147  [MCS:MiddlewareSystem]: Loading middleware chain...  -  Server - MCS_Utils:65
  18:54:15.147  [MCS:Authentication]: Authentication middleware initialized successfully  -  Server - MCS_Utils:65
  18:54:15.147  [MCS:MiddlewareSystem]: Authentication middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:15.147  [MCS:MiddlewareSystem]: LoadMiddleware: Authentication took 0.512 ms  -  Server - MCS_Utils:65
  18:54:15.147  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Authentication took 0.674 ms  -  Server - MCS_Utils:65
  18:54:15.148  [MCS:Permission]: Permission middleware initialized successfully  -  Server - MCS_Utils:65
  18:54:15.148  [MCS:MiddlewareSystem]: Permission middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:15.148  [MCS:MiddlewareSystem]: LoadMiddleware: Permission took 0.204 ms  -  Server - MCS_Utils:65
  18:54:15.148  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Permission took 0.437 ms  -  Server - MCS_Utils:65
  18:54:15.148  [MCS:RateLimiter]: RateLimiterInit took 0.033 ms  -  Server - MCS_Utils:65
  18:54:15.148  [MCS:RateLimiter]: PerformanceMonitor: RateLimiterInit took 0.114 ms  -  Server - MCS_Utils:65
  18:54:15.148  [MCS:RateLimiter]: Rate limiter middleware initialized successfully  -  Server - MCS_Utils:65
  18:54:15.148  [MCS:MiddlewareSystem]: RateLimiter middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:15.148  [MCS:MiddlewareSystem]: LoadMiddleware: RateLimiter took 0.379 ms  -  Server - MCS_Utils:65
  18:54:15.149  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: RateLimiter took 0.447 ms  -  Server - MCS_Utils:65
  18:54:15.149  [MCS:Logger]: Logger initialized successfully  -  Server - MCS_Utils:65
  18:54:15.149  [MCS:MiddlewareSystem]: Logger middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:15.149  [MCS:MiddlewareSystem]: LoadMiddleware: Logger took 0.207 ms  -  Server - MCS_Utils:65
  18:54:15.149  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Logger took 0.310 ms  -  Server - MCS_Utils:65
  18:54:15.149  [MCS:Analytics]: Analytics initialized successfully  -  Server - MCS_Utils:65
  18:54:15.150  [MCS:MiddlewareSystem]: Analytics middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:15.150  [MCS:MiddlewareSystem]: LoadMiddleware: Analytics took 0.564 ms  -  Server - MCS_Utils:65
  18:54:15.150  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Analytics took 0.952 ms  -  Server - MCS_Utils:65
  18:54:15.151  [MCS:InputValidation]: InputValidation middleware initialized successfully  -  Server - MCS_Utils:65
  18:54:15.151  [MCS:MiddlewareSystem]: InputValidation middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:54:15.151  [MCS:MiddlewareSystem]: LoadMiddleware: InputValidation took 0.347 ms  -  Server - MCS_Utils:65
  18:54:15.151  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: InputValidation took 0.424 ms  -  Server - MCS_Utils:65
  18:54:15.151  [MCS:MiddlewareSystem]: Middleware chain loaded with 6 modules  -  Server - MCS_Utils:65
  18:54:15.151  [MCS:MiddlewareSystem]: LoadMiddlewareChain took 4.571 ms  -  Server - MCS_Utils:65
  18:54:15.151  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddlewareChain took 4.636 ms  -  Server - MCS_Utils:65
  18:54:15.151  [MCS:MiddlewareSystem]: Middleware system initialized successfully with 6 modules  -  Server - MCS_Utils:65
  18:54:15.152  [MCS:MiddlewareSystem]: MiddlewareInit took 5.305 ms  -  Server - MCS_Utils:65
  18:54:15.152  [MCS:MiddlewareSystem]: PerformanceMonitor: MiddlewareInit took 5.527 ms  -  Server - MCS_Utils:65
  18:54:15.152  [MCS:MiddlewareSystem]: Middleware system reloaded successfully  -  Server - MCS_Utils:65
  18:54:15.152  [MCS:MCS_Remotes]: Cleaning up remotes system  -  Server - MCS_Utils:65
  18:54:15.152  [MCS:MCS_Remotes]: Remotes system cleaned up  -  Server - MCS_Utils:65
  18:54:15.153  [MCS:MCS_Remotes]: Remotes system initialized successfully  -  Server - MCS_Utils:65
  18:54:15.153  [MCS:ReloadCommand]: System reload completed successfully  -  Server - MCS_Utils:65
  18:54:15.153  [MCS:MCS_Remotes]: Command 'sysres' processed successfully for 1XFPANDAFX1  -  Server - MCS_Utils:65
  18:54:35.691  [MCS:Console]: Console toggled via F2  -  Client - MCS_Utils:65
  18:54:36.215  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  18:54:36.215  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:54:36.512  [MCS:MCS_AutocompleteService]: Getting suggestions for: !r  -  Client - MCS_Utils:65
  18:54:36.638  [MCS:MCS_AutocompleteService]: Getting suggestions for: !re  -  Client - MCS_Utils:65
  18:54:36.913  [MCS:MCS_AutocompleteService]: Getting suggestions for: !rel  -  Client - MCS_Utils:65
  18:54:37.166  [MCS:MCS_AutocompleteService]: Getting suggestions for: !relo  -  Client - MCS_Utils:65
  18:54:37.263  [MCS:MCS_AutocompleteService]: Getting suggestions for: !reloa  -  Client - MCS_Utils:65
  18:54:37.300  [MCS:MCS_AutocompleteService]: Getting suggestions for: !reload  -  Client - MCS_Utils:65
  18:54:37.948  [MCS:MCS_AutocompleteService]: Getting suggestions for: !reloa  -  Client - MCS_Utils:65
  18:54:38.133  [MCS:MCS_AutocompleteService]: Getting suggestions for: !relo  -  Client - MCS_Utils:65
  18:54:38.306  [MCS:MCS_AutocompleteService]: Getting suggestions for: !rel  -  Client - MCS_Utils:65
  18:54:38.465  [MCS:MCS_AutocompleteService]: Getting suggestions for: !re  -  Client - MCS_Utils:65
  18:54:38.635  [MCS:MCS_AutocompleteService]: Getting suggestions for: !r  -  Client - MCS_Utils:65
  18:54:38.881  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  18:54:38.881  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:54:39.180  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  18:54:39.181  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:54:39.912  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  18:54:39.912  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:54:41.292  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  18:54:41.292  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:54:42.994  [MCS:Console]: Console toggled via F2  -  Client - MCS_Utils:65
  18:54:43.595  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  18:54:43.595  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:54:44.495  [MCS:MCS_AutocompleteService]: Getting suggestions for: !b  -  Client - MCS_Utils:65
  18:54:44.712  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ba  -  Client - MCS_Utils:65
  18:54:44.897  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban  -  Client - MCS_Utils:65
  18:54:46.830  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban   -  Client - MCS_Utils:65
  18:54:47.995  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1  -  Client - MCS_Utils:65
  18:54:48.562  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1X  -  Client - MCS_Utils:65
  18:54:48.733  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XF  -  Client - MCS_Utils:65
  18:54:48.897  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFP  -  Client - MCS_Utils:65
  18:54:49.149  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPA  -  Client - MCS_Utils:65
  18:54:49.330  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPAN  -  Client - MCS_Utils:65
  18:54:49.413  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPAND  -  Client - MCS_Utils:65
  18:54:49.512  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDA  -  Client - MCS_Utils:65
  18:54:49.629  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAF  -  Client - MCS_Utils:65
  18:54:49.845  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX  -  Client - MCS_Utils:65
  18:54:50.017  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX1  -  Client - MCS_Utils:65
  18:54:50.499  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX1   -  Client - MCS_Utils:65
  18:54:51.579  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX1 t  -  Client - MCS_Utils:65
  18:54:51.668  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX1 te  -  Client - MCS_Utils:65
  18:54:51.848  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX1 tes  -  Client - MCS_Utils:65
  18:54:51.995  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX1 test  -  Client - MCS_Utils:65
  18:54:52.113  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX1 test   -  Client - MCS_Utils:65
  18:54:52.245  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX1 test 1  -  Client - MCS_Utils:65
  18:54:52.513  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX1 test 1d  -  Client - MCS_Utils:65
  18:54:53.312  [MCS:Console]: Command sent: !ban 1XFPANDAFX1 test 1d  -  Client - MCS_Utils:65
  18:54:53.312  [MCS:MCS_Client_Initialization]: Handling command input: !ban 1XFPANDAFX1 test 1d  -  Client - MCS_Utils:65
  18:54:53.312  [MCS:MCS_CommandParser]: Parsing command: !ban 1XFPANDAFX1 test 1d  -  Client - MCS_Utils:65
  18:54:53.312  [MCS:CommandParser]: parse took 0.008 ms  -  Client - MCS_Utils:65
  18:54:53.312  [MCS:CommandParser]: PerformanceMonitor: parse took 0.076 ms  -  Client - MCS_Utils:65
  18:54:53.312  [MCS:MCS_Client_Initialization]: Sent command to server: ban  -  Client - MCS_Utils:65
  18:54:53.312  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  18:54:53.312  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:55:01.865  [MCS:Console]: Console toggled via F2  -  Client - MCS_Utils:65
  18:55:02.895  [MCS:MCS_AutocompleteService]: Getting suggestions for: 	  -  Client - MCS_Utils:65
  18:55:02.895  [MCS:MCS_AutocompleteService]: Not a command  -  Client - MCS_Utils:65
  18:55:02.895  [MCS:AutocompleteService]: getSuggestions took 0.042 ms  -  Client - MCS_Utils:65
  18:55:02.895  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.072 ms  -  Client - MCS_Utils:65
  18:55:13.596  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  18:55:13.596  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:55:14.479  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  18:55:14.479  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:55:19.045  [MCS:MCS_AutocompleteService]: Getting suggestions for: !g  -  Client - MCS_Utils:65
  18:55:19.198  [MCS:MCS_AutocompleteService]: Getting suggestions for: !gr  -  Client - MCS_Utils:65
  18:55:19.284  [MCS:MCS_AutocompleteService]: Getting suggestions for: !gra  -  Client - MCS_Utils:65
  18:55:19.426  [MCS:MCS_AutocompleteService]: Getting suggestions for: !gran  -  Client - MCS_Utils:65
  18:55:19.529  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grant  -  Client - MCS_Utils:65
  18:55:19.811  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantp  -  Client - MCS_Utils:65
  18:55:19.878  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantpe  -  Client - MCS_Utils:65
  18:55:19.896  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantper  -  Client - MCS_Utils:65
  18:55:20.027  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm  -  Client - MCS_Utils:65
  18:55:21.563  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm   -  Client - MCS_Utils:65
  18:55:21.727  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1  -  Client - MCS_Utils:65
  18:55:22.228  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1X  -  Client - MCS_Utils:65
  18:55:22.426  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XF  -  Client - MCS_Utils:65
  18:55:22.562  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFP  -  Client - MCS_Utils:65
  18:55:22.662  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPA  -  Client - MCS_Utils:65
  18:55:22.830  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPAN  -  Client - MCS_Utils:65
  18:55:22.928  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPAND  -  Client - MCS_Utils:65
  18:55:23.015  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDA  -  Client - MCS_Utils:65
  18:55:23.128  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAF  -  Client - MCS_Utils:65
  18:55:23.327  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX  -  Client - MCS_Utils:65
  18:55:23.477  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1  -  Client - MCS_Utils:65
  18:55:23.628  [MCS:Console]: Command sent: !grantperm 1XFPANDAFX1  -  Client - MCS_Utils:65
  18:55:23.629  [MCS:MCS_Client_Initialization]: Handling command input: !grantperm 1XFPANDAFX1  -  Client - MCS_Utils:65
  18:55:23.629  [MCS:MCS_CommandParser]: Parsing command: !grantperm 1XFPANDAFX1  -  Client - MCS_Utils:65
  18:55:23.629  [MCS:CommandParser]: parse took 0.007 ms  -  Client - MCS_Utils:65
  18:55:23.629  [MCS:CommandParser]: PerformanceMonitor: parse took 0.123 ms  -  Client - MCS_Utils:65
  18:55:23.629  [MCS:MCS_Client_Initialization]: Sent command to server: grantperm  -  Client - MCS_Utils:65
  18:55:23.629  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  18:55:23.629  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65