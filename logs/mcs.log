  18:27:47.988  [MCS:MCS_Server_Initialization]: Starting initialization...  -  Server - MCS_Utils:65
  18:27:47.988  [MCS:PermissionService]: Initialized  -  Server - MCS_Utils:65
  18:27:47.988  [MCS:MiddlewareSystem]: Initializing middleware system...  -  Server - MCS_Utils:65
  18:27:47.988  [MCS:MiddlewareSystem]: Creating fallback middleware implementations...  -  Server - MCS_Utils:65
  18:27:47.988  [MCS:MiddlewareSystem]: Fallback middleware created successfully  -  Server - MCS_Utils:65
  18:27:47.988  [MCS:MiddlewareSystem]: Loading middleware chain...  -  Server - MCS_Utils:65
  18:27:47.989  [MCS:Authentication]: Authentication middleware initialized successfully  -  Server - MCS_Utils:65
  18:27:47.989  [MCS:MiddlewareSystem]: Authentication middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:27:47.989  [MCS:MiddlewareSystem]: LoadMiddleware: Authentication took 0.341 ms  -  Server - MCS_Utils:65
  18:27:47.989  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Authentication took 0.413 ms  -  Server - MCS_Utils:65
  18:27:47.989  [MCS:Permission]: Permission middleware initialized successfully  -  Server - MCS_Utils:65
  18:27:47.989  [MCS:MiddlewareSystem]: Permission middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:27:47.989  [MCS:MiddlewareSystem]: LoadMiddleware: Permission took 0.280 ms  -  Server - MCS_Utils:65
  18:27:47.989  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Permission took 0.317 ms  -  Server - MCS_Utils:65
  18:27:47.990  [MCS:RateLimiter]: RateLimiterInit took 0.005 ms  -  Server - MCS_Utils:65
  18:27:47.990  [MCS:RateLimiter]: PerformanceMonitor: RateLimiterInit took 0.051 ms  -  Server - MCS_Utils:65
  18:27:47.990  [MCS:RateLimiter]: Rate limiter middleware initialized successfully  -  Server - MCS_Utils:65
  18:27:47.990  [MCS:MiddlewareSystem]: RateLimiter middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:27:47.990  [MCS:MiddlewareSystem]: LoadMiddleware: RateLimiter took 0.587 ms  -  Server - MCS_Utils:65
  18:27:47.990  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: RateLimiter took 0.645 ms  -  Server - MCS_Utils:65
  18:27:47.990  [MCS:Logger]: Logger initialized successfully  -  Server - MCS_Utils:65
  18:27:47.990  [MCS:MiddlewareSystem]: Logger middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:27:47.990  [MCS:MiddlewareSystem]: LoadMiddleware: Logger took 0.411 ms  -  Server - MCS_Utils:65
  18:27:47.991  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Logger took 0.477 ms  -  Server - MCS_Utils:65
  18:27:47.991  [MCS:Analytics]: Analytics initialized successfully  -  Server - MCS_Utils:65
  18:27:47.991  [MCS:MiddlewareSystem]: Analytics middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:27:47.991  [MCS:MiddlewareSystem]: LoadMiddleware: Analytics took 0.333 ms  -  Server - MCS_Utils:65
  18:27:47.991  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Analytics took 0.368 ms  -  Server - MCS_Utils:65
  18:27:47.991  [MCS:InputValidation]: InputValidation middleware initialized successfully  -  Server - MCS_Utils:65
  18:27:47.991  [MCS:MiddlewareSystem]: InputValidation middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  18:27:47.991  [MCS:MiddlewareSystem]: LoadMiddleware: InputValidation took 0.246 ms  -  Server - MCS_Utils:65
  18:27:47.991  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: InputValidation took 0.304 ms  -  Server - MCS_Utils:65
  18:27:47.991  [MCS:MiddlewareSystem]: Middleware chain loaded with 6 modules  -  Server - MCS_Utils:65
  18:27:47.992  [MCS:MiddlewareSystem]: LoadMiddlewareChain took 3.112 ms  -  Server - MCS_Utils:65
  18:27:47.992  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddlewareChain took 3.205 ms  -  Server - MCS_Utils:65
  18:27:47.992  [MCS:MiddlewareSystem]: Middleware system initialized successfully with 6 modules  -  Server - MCS_Utils:65
  18:27:47.992  [MCS:MiddlewareSystem]: MiddlewareInit took 3.602 ms  -  Server - MCS_Utils:65
  18:27:47.992  [MCS:MiddlewareSystem]: PerformanceMonitor: MiddlewareInit took 3.709 ms  -  Server - MCS_Utils:65
  18:27:47.992  [MCS:CommandDispatcher]: Registered command: ban from Ban  -  Server - MCS_Utils:65
  18:27:47.992  [MCS:CommandDispatcher]: Registered command: reload from Reload  -  Server - MCS_Utils:65
  18:27:47.992  [MCS:CommandDispatcher]: Registered alias: sysres -> reload  -  Server - MCS_Utils:65
  18:27:47.993  [MCS:CommandDispatcher]: Registered command: grantperm from GrantPerm  -  Server - MCS_Utils:65
  18:27:47.993  [MCS:CommandDispatcher]: Registered command: revokeperm from RevokePerm  -  Server - MCS_Utils:65
  18:27:47.993  [MCS:MCS_Remotes]: Remotes system initialized successfully  -  Server - MCS_Utils:65
  18:27:47.993  [MCS:MCS_Server_Initialization]: Initialization completed.  -  Server - MCS_Utils:65
  18:27:48.167  [MCS:MCS_CommandParser]: Module initialized  -  Client - MCS_Utils:65
  18:27:48.167  [MCS:MCS_AutocompleteService]: Module initialized  -  Client - MCS_Utils:65
  18:27:52.660  [MCS:MiddlewareSystem]: Middleware system already initialized  -  Server - MCS_Utils:65
  18:27:52.859  [MCS:Console]: Initializing...  -  Client - MCS_Utils:65
  18:27:52.923  [MCS:FeedbackDisplay]: Initializing...  -  Client - MCS_Utils:65
  18:27:52.923  [MCS:MCS_AutocompleteService]: Initializing service  -  Client - MCS_Utils:65
  18:27:52.923  [MCS:MCS_AutocompleteService]: Service initialized successfully  -  Client - MCS_Utils:65
  18:27:52.923  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  18:27:52.923  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:27:52.923  [MCS:MCS_Client_Initialization]: MCS Client initialized  -  Client - MCS_Utils:65
  18:27:52.943  [MCS:MCS_Remotes]: Returned 5 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  18:27:52.977  [MCS:MCS_AutocompleteService]: Fetched 5 available commands  -  Client - MCS_Utils:65
  18:27:52.977  [MCS:AutocompleteService]: getAvailableCommands took 53.384 ms  -  Client - MCS_Utils:65
  18:27:52.977  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 53.523 ms  -  Client - MCS_Utils:65
  18:27:52.977  [MCS:MCS_AutocompleteService]: Returning 5 suggestions for prefix  -  Client - MCS_Utils:65
  18:27:52.977  [MCS:AutocompleteService]: getSuggestions took 53.846 ms  -  Client - MCS_Utils:65
  18:27:52.977  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 53.968 ms  -  Client - MCS_Utils:65
  18:27:54.190  [MCS:Console]: Console toggled via F2  -  Client - MCS_Utils:65
  18:27:54.907  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  18:27:54.907  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:27:54.938  [MCS:MCS_Remotes]: Returned 5 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  18:27:54.971  [MCS:MCS_AutocompleteService]: Fetched 5 available commands  -  Client - MCS_Utils:65
  18:27:54.971  [MCS:AutocompleteService]: getAvailableCommands took 64.044 ms  -  Client - MCS_Utils:65
  18:27:54.971  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 64.145 ms  -  Client - MCS_Utils:65
  18:27:54.971  [MCS:MCS_AutocompleteService]: Returning 5 suggestions for prefix  -  Client - MCS_Utils:65
  18:27:54.972  [MCS:AutocompleteService]: getSuggestions took 64.430 ms  -  Client - MCS_Utils:65
  18:27:54.972  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 64.527 ms  -  Client - MCS_Utils:65
  18:27:56.656  [MCS:MCS_AutocompleteService]: Getting suggestions for: !!sysres  -  Client - MCS_Utils:65
  18:27:56.688  [MCS:MCS_Remotes]: Returned 1 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  18:27:56.721  [MCS:MCS_AutocompleteService]: Fetched 1 suggestions from server  -  Client - MCS_Utils:65
  18:27:56.721  [MCS:AutocompleteService]: getSuggestions took 64.720 ms  -  Client - MCS_Utils:65
  18:27:56.721  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 64.769 ms  -  Client - MCS_Utils:65
  18:27:57.756  [MCS:MCS_AutocompleteService]: Getting suggestions for: !!sysres   -  Client - MCS_Utils:65
  18:27:57.756  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:27:57.756  [MCS:AutocompleteService]: getSuggestions took 0.079 ms  -  Client - MCS_Utils:65
  18:27:57.756  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.126 ms  -  Client - MCS_Utils:65
  18:27:59.307  [MCS:MCS_AutocompleteService]: Getting suggestions for: !!sysres  -  Client - MCS_Utils:65
  18:27:59.307  [MCS:MCS_AutocompleteService]: Returning 1 cached suggestions  -  Client - MCS_Utils:65
  18:27:59.308  [MCS:AutocompleteService]: getSuggestions took 0.116 ms  -  Client - MCS_Utils:65
  18:27:59.308  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.206 ms  -  Client - MCS_Utils:65
  18:27:59.741  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  18:27:59.741  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:27:59.771  [MCS:MCS_Remotes]: Returned 5 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  18:27:59.788  [MCS:MCS_AutocompleteService]: Fetched 5 available commands  -  Client - MCS_Utils:65
  18:27:59.788  [MCS:AutocompleteService]: getAvailableCommands took 46.786 ms  -  Client - MCS_Utils:65
  18:27:59.788  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 46.911 ms  -  Client - MCS_Utils:65
  18:27:59.788  [MCS:MCS_AutocompleteService]: Returning 5 suggestions for prefix  -  Client - MCS_Utils:65
  18:27:59.788  [MCS:AutocompleteService]: getSuggestions took 47.305 ms  -  Client - MCS_Utils:65
  18:27:59.788  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 47.437 ms  -  Client - MCS_Utils:65
  18:28:00.444  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  18:28:00.444  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:28:00.471  [MCS:MCS_Remotes]: Returned 5 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  18:28:00.488  [MCS:MCS_AutocompleteService]: Fetched 5 available commands  -  Client - MCS_Utils:65
  18:28:00.488  [MCS:AutocompleteService]: getAvailableCommands took 43.537 ms  -  Client - MCS_Utils:65
  18:28:00.488  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 43.698 ms  -  Client - MCS_Utils:65
  18:28:00.488  [MCS:MCS_AutocompleteService]: Returning 5 suggestions for prefix  -  Client - MCS_Utils:65
  18:28:00.488  [MCS:AutocompleteService]: getSuggestions took 44.119 ms  -  Client - MCS_Utils:65
  18:28:00.489  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 44.344 ms  -  Client - MCS_Utils:65
  18:28:00.890  [MCS:MCS_AutocompleteService]: Getting suggestions for: !s  -  Client - MCS_Utils:65
  18:28:00.890  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:28:00.890  [MCS:AutocompleteService]: getSuggestions took 0.062 ms  -  Client - MCS_Utils:65
  18:28:00.890  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.099 ms  -  Client - MCS_Utils:65
  18:28:00.973  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sy  -  Client - MCS_Utils:65
  18:28:00.973  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:28:00.973  [MCS:AutocompleteService]: getSuggestions took 0.111 ms  -  Client - MCS_Utils:65
  18:28:00.974  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.256 ms  -  Client - MCS_Utils:65
  18:28:01.140  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sys  -  Client - MCS_Utils:65
  18:28:01.140  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:28:01.140  [MCS:AutocompleteService]: getSuggestions took 0.138 ms  -  Client - MCS_Utils:65
  18:28:01.140  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.230 ms  -  Client - MCS_Utils:65
  18:28:01.323  [MCS:MCS_AutocompleteService]: Getting suggestions for: !syst  -  Client - MCS_Utils:65
  18:28:01.323  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:28:01.323  [MCS:AutocompleteService]: getSuggestions took 0.064 ms  -  Client - MCS_Utils:65
  18:28:01.323  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.217 ms  -  Client - MCS_Utils:65
  18:28:01.373  [MCS:MCS_AutocompleteService]: Getting suggestions for: !syste  -  Client - MCS_Utils:65
  18:28:01.373  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:28:01.373  [MCS:AutocompleteService]: getSuggestions took 0.062 ms  -  Client - MCS_Utils:65
  18:28:01.373  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.105 ms  -  Client - MCS_Utils:65
  18:28:01.773  [MCS:MCS_AutocompleteService]: Getting suggestions for: !syst  -  Client - MCS_Utils:65
  18:28:01.773  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:28:01.773  [MCS:AutocompleteService]: getSuggestions took 0.090 ms  -  Client - MCS_Utils:65
  18:28:01.773  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.171 ms  -  Client - MCS_Utils:65
  18:28:01.942  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sys  -  Client - MCS_Utils:65
  18:28:01.942  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:28:01.942  [MCS:AutocompleteService]: getSuggestions took 0.082 ms  -  Client - MCS_Utils:65
  18:28:01.942  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.126 ms  -  Client - MCS_Utils:65
  18:28:02.122  [MCS:MCS_AutocompleteService]: Getting suggestions for: !sy  -  Client - MCS_Utils:65
  18:28:02.122  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:28:02.122  [MCS:AutocompleteService]: getSuggestions took 0.118 ms  -  Client - MCS_Utils:65
  18:28:02.122  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.150 ms  -  Client - MCS_Utils:65
  18:28:02.293  [MCS:MCS_AutocompleteService]: Getting suggestions for: !s  -  Client - MCS_Utils:65
  18:28:02.293  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:28:02.293  [MCS:AutocompleteService]: getSuggestions took 0.044 ms  -  Client - MCS_Utils:65
  18:28:02.293  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.144 ms  -  Client - MCS_Utils:65
  18:28:02.473  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  18:28:02.473  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  18:28:02.504  [MCS:MCS_Remotes]: Returned 5 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  18:28:02.538  [MCS:MCS_AutocompleteService]: Fetched 5 available commands  -  Client - MCS_Utils:65
  18:28:02.538  [MCS:AutocompleteService]: getAvailableCommands took 64.961 ms  -  Client - MCS_Utils:65
  18:28:02.538  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 65.053 ms  -  Client - MCS_Utils:65
  18:28:02.538  [MCS:MCS_AutocompleteService]: Returning 5 suggestions for prefix  -  Client - MCS_Utils:65
  18:28:02.538  [MCS:AutocompleteService]: getSuggestions took 65.434 ms  -  Client - MCS_Utils:65
  18:28:02.538  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 65.476 ms  -  Client - MCS_Utils:65
  18:28:04.157  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban  -  Client - MCS_Utils:65
  18:28:04.157  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:28:04.157  [MCS:AutocompleteService]: getSuggestions took 0.102 ms  -  Client - MCS_Utils:65
  18:28:04.157  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.196 ms  -  Client - MCS_Utils:65
  18:28:04.623  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban   -  Client - MCS_Utils:65
  18:28:04.623  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  18:28:04.623  [MCS:AutocompleteService]: getSuggestions took 0.057 ms  -  Client - MCS_Utils:65
  18:28:04.623  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.118 ms  -  Client - MCS_Utils:65
  18:28:08.177  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1  -  Client - MCS_Utils:65
  18:28:08.207  ServerScriptService.MCS.Core.MCS_CommandDispatcher:313: invalid argument #1 to 'ipairs' (table expected, got nil)  -  Server - MCS_CommandDispatcher:313
  18:28:08.210  Script 'ServerScriptService.MCS.Core.MCS_CommandDispatcher', Line 313 - function getAutocompleteSuggestions  -  Studio - MCS_CommandDispatcher:313
  18:28:08.211  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 182 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:182
  18:28:08.211  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 361  -  Studio - MCS_Remotes:361
  18:28:08.260  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ServerScriptService.MCS.Core.MCS_CommandDispatcher:313: invalid argument #1 to 'ipairs' (table expected, got nil)  -  Client - MCS_Utils:65
  18:28:08.260  [MCS:AutocompleteService]: getSuggestions took 83.582 ms  -  Client - MCS_Utils:65
  18:28:08.261  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 83.654 ms  -  Client - MCS_Utils:65
  18:28:12.444  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1X  -  Client - MCS_Utils:65
  18:28:12.471  ServerScriptService.MCS.Core.MCS_CommandDispatcher:313: invalid argument #1 to 'ipairs' (table expected, got nil)  -  Server - MCS_CommandDispatcher:313
  18:28:12.471  Script 'ServerScriptService.MCS.Core.MCS_CommandDispatcher', Line 313 - function getAutocompleteSuggestions  -  Studio - MCS_CommandDispatcher:313
  18:28:12.471  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 182 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:182
  18:28:12.471  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 361  -  Studio - MCS_Remotes:361
  18:28:12.488  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ServerScriptService.MCS.Core.MCS_CommandDispatcher:313: invalid argument #1 to 'ipairs' (table expected, got nil)  -  Client - MCS_Utils:65
  18:28:12.488  [MCS:AutocompleteService]: getSuggestions took 43.791 ms  -  Client - MCS_Utils:65
  18:28:12.489  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 43.977 ms  -  Client - MCS_Utils:65
  18:28:12.879  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XF  -  Client - MCS_Utils:65
  18:28:12.905  ServerScriptService.MCS.Core.MCS_CommandDispatcher:313: invalid argument #1 to 'ipairs' (table expected, got nil)  -  Server - MCS_CommandDispatcher:313
  18:28:12.905  Script 'ServerScriptService.MCS.Core.MCS_CommandDispatcher', Line 313 - function getAutocompleteSuggestions  -  Studio - MCS_CommandDispatcher:313
  18:28:12.905  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 182 - function handleAutocompleteRequest  -  Studio - MCS_Remotes:182
  18:28:12.905  Script 'ReplicatedStorage.MCS.MCS_Remotes', Line 361  -  Studio - MCS_Remotes:361
  18:28:12.938  [MCS:MCS_AutocompleteService]: Failed to fetch suggestions from server: ServerScriptService.MCS.Core.MCS_CommandDispatcher:313: invalid argument #1 to 'ipairs' (table expected, got nil)  -  Client - MCS_Utils:65
  18:28:12.938  [MCS:AutocompleteService]: getSuggestions took 59.122 ms  -  Client - MCS_Utils:65
  18:28:12.938  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 59.200 ms  -  Client - MCS_Utils:65
  18:28:24.173  [MCS:Console]: Close button clicked, closing console  -  Client - MCS_Utils:65
  18:29:37.939  [MCS:RateLimiter]: Cleaned up rate limit data for 1XFPANDAFX1 (1703810675)  -  Server - MCS_Utils:65
