--!strict

--[[
    - file: MCS_CommandDispatcher.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Command Dispatch System for the Modular Command System (MCS)
      - Central hub for command registration, processing, and execution:
        - Commands validation, sanitizing, security etc.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Middleware =
  require(script.Parent:WaitForChild("Middleware"):WaitForChild("MCS_Middleware_Initialization"))
local PermissionService = require(script.Parent:WaitForChild("MCS_PermissionService"))
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS:WaitForChild("Shared"):WaitForChild("MCS_Utils"))

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local CommandDispatcher = {}

-- ============================================================================
-- VARIABLES
-- ============================================================================
local commandRegistry: Types.CommandRegistry = {}
local aliasLookup: Types.AliasLookup = {}

local commandCategories: { [string]: Types.CommandCategory } = {
  SYSTEM = "System" :: Types.CommandCategory,
  MODERATION = "Moderation" :: Types.CommandCategory,
  UTILITY = "Utility" :: Types.CommandCategory,
  DEVELOPER = "Developer" :: Types.CommandCategory,
}

-- ============================================================================
-- PRIVATE FUNCTIONS
-- ============================================================================

-- Function to parse command string into command name and arguments
local function parseCommand(commandText: string): (string?, { string }?)
  Utils.startTimer("CommandDispatcher", "parseCommand")

  -- Validate input
  if type(commandText) ~= "string" then
    Utils.endTimer("CommandDispatcher", "parseCommand")
    return nil, nil
  end

  -- Strip prefix and get command text
  local text = Utils.stripPrefix(commandText, Configuration.Commands.PREFIX) or ""
  if text == "" then
    Utils.endTimer("CommandDispatcher", "parseCommand")
    return nil, nil
  end

  -- Split into tokens
  local tokens = Utils.splitCommandText(text)
  if #tokens == 0 then
    Utils.endTimer("CommandDispatcher", "parseCommand")
    return nil, nil
  end

  -- Get base command name and arguments
  local commandName = tokens[1]:lower()
  local args = table.pack(unpack(tokens, 2)) :: { string }

  Utils.endTimer("CommandDispatcher", "parseCommand")
  return commandName, args
end

-- Parse Command Version
local function parseVersion(commandName: string): (string, number?)
  local base, version = commandName:match("^(.-)_v(%d+)$")
  return base or commandName, tonumber(version)
end

-- ============================================================================
-- PUBLIC FUNCTIONS
-- ============================================================================

-- Get Command List
function CommandDispatcher.getCommandList(): { string }
  local commands: { string } = {}
  for name, _ in pairs(commandRegistry) do
    table.insert(commands, name)
  end
  return commands
end

-- Get command Module Data
function CommandDispatcher.getCommandModule(commandName: string): Types.CommandModule?
  local commandData = commandRegistry[commandName:lower()]
  if commandData then
    return commandData.module
  end
  return nil
end

-- Initialize by loading all command modules
function CommandDispatcher.init()
  Utils.startTimer("CommandDispatcher", "init")

  -- Clear existing registries
  commandRegistry = {}
  aliasLookup = {}

  -- Commands Folder for loading
  local mcsFolder = ServerStorage:FindFirstChild("MCS")
  if not mcsFolder then
    Utils.endTimer("CommandDispatcher", "init")
    return
  end

  local commandsFolder = mcsFolder:FindFirstChild("Commands")
  if not commandsFolder then
    Utils.endTimer("CommandDispatcher", "init")
    return
  end

  -- Function to recursively search for command modules
  local function loadCommandsFromFolder(folder: Folder)
    for _, item in ipairs(folder:GetChildren()) do
      if item:IsA("ModuleScript") then
        local baseName = item.Name:lower()
        local cleanName, version = parseVersion(baseName)
        local existing = commandRegistry[cleanName]

        if not existing or (version and version > (existing.version or 0)) then
          local success, loadedModule = pcall(require, item)
          if not success then
            Utils.print(
              "CommandDispatcher",
              "Failed to load command " .. item.Name .. ": " .. tostring(loadedModule)
            )
            continue
          end
          if type(loadedModule) ~= "table" then
            Utils.print("CommandDispatcher", "Command " .. item.Name .. " did not return a table")
            continue
          end

          -- Validate command module
          if type(loadedModule.Execute) ~= "function" then
            Utils.print("CommandDispatcher", "Command " .. item.Name .. " missing Execute function")
            continue
          end

          -- Explicitly cast to CommandModule type
          local commandModule: Types.CommandModule = loadedModule :: Types.CommandModule

          -- Handle aliases with proper typing
          local aliases: { string } = {}
          if type(commandModule.Aliases) == "table" then
            for _, alias in ipairs(commandModule.Aliases) do
              if type(alias) == "string" then
                table.insert(aliases, alias)
              end
            end
          end

          -- Register permission
          local permissionLevel = commandModule.PermissionLevel or PermissionService.Levels.PLAYER
          PermissionService.registerCommandPermission(cleanName, permissionLevel)

          -- Create registry entry with explicit type
          local registryEntry: Types.CommandRegistryEntry = {
            module = commandModule,
            version = version or 1,
            aliases = aliases,
            path = item:GetFullName(),
          }

          commandRegistry[cleanName] = registryEntry
          Utils.print(
            "CommandDispatcher",
            "Registered command: " .. cleanName .. " from " .. item.Name
          )

          -- Register aliases
          for _, alias in ipairs(aliases) do
            aliasLookup[alias:lower()] = cleanName
            Utils.print("CommandDispatcher", "Registered alias: " .. alias .. " -> " .. cleanName)
          end
        end
      elseif item:IsA("Folder") then
        -- Recursively load commands from subfolders
        loadCommandsFromFolder(item)
      end
    end
  end

  loadCommandsFromFolder(commandsFolder)
  Utils.endTimer("CommandDispatcher", "init")
end

-- Process a command from a player
function CommandDispatcher.processCommand(player: Player, commandText: string): (boolean, string?)
  Utils.startTimer("CommandDispatcher", "processCommand")

  -- Validate input
  if not player or not player.Parent then
    Utils.endTimer("CommandDispatcher", "processCommand")
    return false, "Invalid player"
  end

  if type(commandText) ~= "string" then
    Utils.endTimer("CommandDispatcher", "processCommand")
    return false, "Invalid command text"
  end

  local commandName, args = parseCommand(commandText)
  if not commandName then
    Utils.endTimer("CommandDispatcher", "processCommand")
    return false, "Invalid command format"
  end

  Utils.print("CommandDispatcher", "Looking for command: " .. commandName)

  -- Resolve command through aliases
  local resolvedCommand = aliasLookup[commandName] or commandName
  Utils.print("CommandDispatcher", "Resolved to: " .. resolvedCommand)

  local commandData = commandRegistry[resolvedCommand]
  if not commandData then
    Utils.print(
      "CommandDispatcher",
      "Available commands: " .. table.concat(CommandDispatcher.getCommandList(), ", ")
    )
    Utils.endTimer("CommandDispatcher", "processCommand")
    return false, "Unknown command: " .. commandName
  end

  -- Run middleware chain
  local success, errorMessage, data = Middleware.run(player, resolvedCommand, args)
  if not success then
    Utils.endTimer("CommandDispatcher", "processCommand")
    return false, errorMessage or "Command execution denied"
  end

  -- Execute command
  local executeSuccess, result = pcall(function()
    return commandData.module.Execute(player, args or {}, Configuration.Commands.PREFIX)
  end)

  if not executeSuccess then
    Utils.endTimer("CommandDispatcher", "processCommand")
    return false, "Command execution error"
  end

  if type(result) ~= "table" or type(result.success) ~= "boolean" then
    Utils.endTimer("CommandDispatcher", "processCommand")
    return false, "Command returned invalid result"
  end

  Utils.endTimer("CommandDispatcher", "processCommand")
  return result.success, result.message
end

-- Get autocomplete suggestions for partial command
function CommandDispatcher.getAutocompleteSuggestions(
  player: Player,
  partialCommand: string
): { string }
  Utils.startTimer("CommandDispatcher", "getAutocompleteSuggestions")

  -- Handle empty input
  if partialCommand == "" then
    Utils.endTimer("CommandDispatcher", "getAutocompleteSuggestions")
    return {}
  end

  local prefix: string = Configuration.Commands.PREFIX
  local stripped: string = Utils.stripPrefix(partialCommand, prefix) or ""
  local searchText: string = stripped:lower()

  -- Handle player suggestions for ban/kick commands
  if #stripped > 0 then
    local tokens = Utils.splitCommandText(stripped)
    if #tokens >= 2 then
      local commandName: string? = tokens[1]
      local argText: string? = tokens[2]

      if commandName and argText then
        -- Check if it's a command that accepts player arguments
        local isPlayerCommand = false
        for _, cmd in ipairs(Configuration.Commands.PLAYER_ARGUMENT_COMMANDS) do
          if commandName == cmd then
            isPlayerCommand = true
            break
          end
        end

        if isPlayerCommand then
          local playerNames: { string } = {}
          local partialName = argText:lower()

          for _, targetPlayer in ipairs(Players:GetPlayers()) do
            if Utils.playerNameMatches(targetPlayer, partialName) then
              table.insert(playerNames, prefix .. commandName .. " " .. targetPlayer.Name)
            end
          end

          Utils.endTimer("CommandDispatcher", "getAutocompleteSuggestions")
          return playerNames
        end
      end
    end
  end

  -- Generate command suggestions
  local rawSuggestions: { string } = {}
  for commandName, commandData in pairs(commandRegistry) do
    table.insert(rawSuggestions, prefix .. commandName)
    for _, alias in ipairs(commandData.aliases or {}) do
      table.insert(rawSuggestions, prefix .. alias)
    end
  end

  -- Filter and limit suggestions
  local filtered = Utils.filterSuggestions(rawSuggestions, searchText)
  local limited = Utils.tableLimit(filtered, Configuration.Commands.MAX_SUGGESTIONS)

  Utils.endTimer("CommandDispatcher", "getAutocompleteSuggestions")
  return limited
end

-- Dynamic Loading (hot-reload)
function CommandDispatcher.reload()
  commandRegistry = {}
  aliasLookup = {}
  CommandDispatcher.init()
  return true, "Command system reloaded"
end

-- Return available commands for the player
function CommandDispatcher.getAvailableCommands(player: Player): { Types.AvailableCommand }
  local availableCommands: { Types.AvailableCommand } = {}

  for commandName, commandData in pairs(commandRegistry) do
    if PermissionService.canUseCommand(player, commandName) then
      local baseUsage = commandData.module.Usage or commandName .. " [args]"
      table.insert(availableCommands, {
        name = commandName,
        description = commandData.module.Description or "No description",
        usage = Configuration.Commands.PREFIX .. baseUsage,
        aliases = commandData.aliases or {},
      })
    end
  end

  return availableCommands
end

-- Check if player can use a specific command
function CommandDispatcher.canUseCommand(player: Player, commandName: string): boolean
  return PermissionService.canUseCommand(player, commandName)
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return CommandDispatcher
