--!strict

--[[
    - file: MCS_Client_Initialization.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Initializes all client-side components for the Modular Command System (MCS)
      - Sets up UI, command parsing, autocomplete, and remote event handling
      - Delegates validation and execution to server-side logic
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

local AutocompleteService = require(script.Parent.Core:WaitForChild("MCS_AutocompleteService"))
local CommandParser = require(script.Parent.Core:WaitForChild("MCS_CommandParser"))
local Console = require(script.Parent.UI:WaitForChild("MCS_Console_UI"))
local FeedbackDisplay = require(script.Parent.UI:WaitForChild("MCS_Feedback_UI"))

-- ============================================================================
-- REMOTES
-- ============================================================================
local remotes: Folder = ReplicatedStorage:WaitForChild("MCS"):WaitForChild("Remotes")
local commandRemote: RemoteEvent = remotes:WaitForChild("CommandRemote") :: RemoteEvent
local autocompleteRemote: RemoteFunction =
  remotes:WaitForChild("AutocompleteRemote") :: RemoteFunction
local checkConsolePermission: RemoteFunction =
  remotes:WaitForChild("CheckConsolePermission") :: RemoteFunction

-- Constants
local SCRIPT_NAME = "MCS_Client_Initialization" :: string

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Initialize UI components
Console.init()
FeedbackDisplay.init()

-- Initialize AutocompleteService
AutocompleteService.init(autocompleteRemote)

-- Preload command suggestions
task.spawn(function()
  AutocompleteService.getSuggestions(Configuration.Commands.PREFIX)
end)

-- ============================================================================
-- COMMAND HANDLING
-- ============================================================================

-- Handle command input from various sources
local function handleCommandInput(text: string)
  Utils.print(SCRIPT_NAME, "Handling command input: " .. text)
  Utils.startTimer(SCRIPT_NAME, "handleCommandInput")

  -- Sanitize input
  local sanitizedText = Utils.sanitize(text)
  if sanitizedText ~= text then
    Utils.print(SCRIPT_NAME, "Input sanitized due to invalid characters")
  end

  -- Check if it's a command
  if not CommandParser.isCommand(sanitizedText) then
    Utils.print(SCRIPT_NAME, "Not a command")
    Utils.endTimer(SCRIPT_NAME, "handleCommandInput")
    return
  end

  -- Parse the command
  local commandRequest: Types.CommandRequest? = CommandParser.parse(sanitizedText)
  if not commandRequest then
    FeedbackDisplay.showFeedback(false, "Invalid command format")
    Utils.endTimer(SCRIPT_NAME, "handleCommandInput")
    return
  end

  -- Send to server
  commandRemote:FireServer(commandRequest)
  Utils.print(SCRIPT_NAME, "Sent command to server: " .. commandRequest.commandName)
  Utils.endTimer(SCRIPT_NAME, "handleCommandInput")
end

-- ============================================================================
-- CONSOLE KEYBIND
-- ============================================================================

local function onInputBegan(input: InputObject, gameProcessed: boolean)
  if gameProcessed or input.KeyCode ~= Enum.KeyCode.F2 then
    return
  end

  local success, hasPermission = pcall(checkConsolePermission.InvokeServer, checkConsolePermission)
  if success and hasPermission then
    Console.show()
    Utils.print(SCRIPT_NAME, "Opened console")
  else
    FeedbackDisplay.showFeedback(false, "You don't have permission to open the console")
    Utils.print(SCRIPT_NAME, "Console access denied")
  end
end

UserInputService.InputBegan:Connect(onInputBegan)

-- ============================================================================
-- SERVER FEEDBACK
-- ============================================================================

commandRemote.OnClientEvent:Connect(function(response: Types.CommandResponse)
  Utils.print(SCRIPT_NAME, "Received server response: " .. tostring(response.success))
  FeedbackDisplay.showFeedback(response.success, response.message or "No message provided")
end)

-- ============================================================================
-- MODULE EXPORTS
-- ============================================================================
local MCSClient = {}

-- Export the command input handler for use by UI components
MCSClient.handleCommandInput = handleCommandInput

-- ============================================================================
-- MODULE INITIALIZATION
-- ============================================================================
Utils.print(SCRIPT_NAME, "MCS Client initialized")

return MCSClient
