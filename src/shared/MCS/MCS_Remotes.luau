--!strict

--[[
    - file: MCS_Remotes.luau

    - version: 1.0.1
    - author: BleckWolf25
    - contributors: Grok (xAI)

    - copyright: Dynamic Innovative Studio

    - description:
      - Remote Event and Function handler for Modular Command System (MCS)
      - Manages client-server communication for command execution and autocompletion
      - Creates remote structure under ReplicatedStorage.MCS.Remotes
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")

-- ============================================================================
-- MODULES
-- ============================================================================
local CommandDispatcher =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_CommandDispatcher"))
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local PermissionService =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PermissionService"))
local Types = require(ReplicatedStorage:WaitForChild("MCS"):WaitForChild("MCS_Types"))
local Utils =
  require(ReplicatedStorage:WaitForChild("MCS"):WaitForChild("Shared"):WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local MODULE_NAME = "MCS_Remotes"
local DEFAULT_PREFIX = Configuration.Commands.PREFIX

-- ============================================================================
-- VARIABLES
-- ============================================================================
local isInitialized: boolean = false
local remoteFolder: Folder?
local commandRemote: RemoteEvent?
local autocompleteRemote: RemoteFunction?
local checkConsolePermission: RemoteFunction?

-- ============================================================================
-- PRIVATE FUNCTIONS
-- ============================================================================

-- Validate command request structure
local function validateCommandRequest(request: Types.CommandRequest): (boolean, string?)
  if type(request) ~= "table" then
    return false, "Invalid request format"
  end

  if type(request.commandName) ~= "string" or request.commandName == "" then
    return false, "Invalid command name"
  end

  if request.args and type(request.args) ~= "table" then
    return false, "Invalid arguments format"
  end

  local sanitizedCommandName = Utils.sanitize(request.commandName) :: string
  if sanitizedCommandName == "" then
    return false, "Sanitized command name is empty"
  end
  request.commandName = sanitizedCommandName

  if request.args then
    local maxArgs = Configuration.Commands.MAX_ARGS or 10
    if #request.args > maxArgs then
      return false, string.format("Too many arguments. Maximum allowed: %d", maxArgs)
    end

    for i, arg in ipairs(request.args) do
      if type(arg) ~= "string" then
        return false, "Invalid argument type at index " .. tostring(i)
      end
      local sanitizedArg = Utils.sanitize(arg) :: string
      if sanitizedArg == "" then
        return false, "Sanitized argument at index " .. tostring(i) .. " is empty"
      end
      request.args[i] = sanitizedArg
    end
  end

  return true, nil
end

-- Handle command submission from client
local function handleCommandRequest(
  player: Player,
  request: Types.CommandRequest
): Types.CommandResponse
  Utils.startTimer(MODULE_NAME, "HandleCommandRequest")

  if not player or not player.Parent then
    Utils.endTimer(MODULE_NAME, "HandleCommandRequest")
    return { success = false, message = "Invalid player" }
  end

  local isValid, errorMessage = validateCommandRequest(request)
  if not isValid then
    Utils.print(
      MODULE_NAME,
      string.format("Invalid command request from %s: %s", player.Name, errorMessage or "unknown")
    )
    Utils.endTimer(MODULE_NAME, "HandleCommandRequest")
    return { success = false, message = errorMessage or "Invalid request" }
  end

  -- Use CommandDispatcher to process the command (includes middleware + execution)
  local commandText = Configuration.Commands.PREFIX .. request.commandName
  if request.args and #request.args > 0 then
    commandText = commandText .. " " .. table.concat(request.args, " ")
  end

  local success, message = CommandDispatcher.processCommand(player, commandText)

  if not success then
    Utils.print(
      MODULE_NAME,
      string.format(
        "Command '%s' failed for %s: %s",
        request.commandName,
        player.Name,
        message or "unknown"
      )
    )
  else
    Utils.print(
      MODULE_NAME,
      string.format("Command '%s' processed successfully for %s", request.commandName, player.Name)
    )
  end

  Utils.endTimer(MODULE_NAME, "HandleCommandRequest")
  return { success = success, message = message }
end

-- Handle autocomplete requests from client
local function handleAutocompleteRequest(player: Player, input: string): Types.QueryResponse
  Utils.startTimer(MODULE_NAME, "HandleAutocompleteRequest")

  if not player or not player.Parent then
    Utils.endTimer(MODULE_NAME, "HandleAutocompleteRequest")
    return { success = false, message = "Invalid player" }
  end

  if type(input) ~= "string" then
    Utils.endTimer(MODULE_NAME, "HandleAutocompleteRequest")
    return { success = false, message = "Invalid input type" }
  end

  local sanitizedInput = Utils.sanitize(input) :: string
  if sanitizedInput == "" then
    Utils.endTimer(MODULE_NAME, "HandleAutocompleteRequest")
    return { success = false, message = "Empty input" }
  end

  local success, commands = pcall(PermissionService.getAccessibleCommands, player)
  if not success then
    Utils.print(
      MODULE_NAME,
      string.format("Failed to retrieve commands for %s: %s", player.Name, tostring(commands))
    )
    Utils.endTimer(MODULE_NAME, "HandleAutocompleteRequest")
    return { success = false, message = "Failed to retrieve commands" }
  end

  -- Use CommandDispatcher's autocomplete function for better player name suggestions
  local autocompleteSuggestions =
    CommandDispatcher.getAutocompleteSuggestions(player, sanitizedInput)
  local suggestions: { Types.AvailableCommand } = {}
  local maxSuggestions = Configuration.Commands.MAX_SUGGESTIONS or 5

  -- Convert string suggestions to AvailableCommand format
  for i, suggestionText in ipairs(autocompleteSuggestions) do
    if i > maxSuggestions then
      break
    end

    -- Extract command name from suggestion (remove prefix)
    local prefix = Configuration.Commands.PREFIX or DEFAULT_PREFIX
    local commandName = suggestionText:gsub("^" .. Utils.escapePattern(prefix), ""):match("^(%S+)")

    if commandName then
      -- Try to resolve alias to actual command name
      local resolvedCommandName = CommandDispatcher.resolveAlias(commandName) or commandName
      local commandModule = CommandDispatcher.getCommandModule(resolvedCommandName)

      if commandModule then
        local commandInfo: Types.AvailableCommand = {
          name = resolvedCommandName,
          description = commandModule.Description or "No description available",
          usage = suggestionText,
          aliases = commandModule.Aliases or {},
        }
        table.insert(suggestions, commandInfo)
      else
        -- Fallback for suggestions that don't match a command module (like player names)
        local commandInfo: Types.AvailableCommand = {
          name = suggestionText,
          description = "Suggestion",
          usage = suggestionText,
          aliases = {},
        }
        table.insert(suggestions, commandInfo)
      end
    end
  end

  -- If no autocomplete suggestions, fall back to command list
  if #suggestions == 0 then
    -- Split input into tokens (after stripping prefix)
    local inputWithoutPrefix: string = Utils.stripPrefix(sanitizedInput) or ""
    local tokens = Utils.splitCommandText(inputWithoutPrefix)
    local inputLower = inputWithoutPrefix:lower()

    if #tokens == 1 then
      -- Command suggestions
      local commandList = CommandDispatcher.getCommandList() :: { string }
      for _, commandName: string in ipairs(commandList) do
        local commandModule: Types.CommandModule? = CommandDispatcher.getCommandModule(commandName)
        if commandModule then
          local matches = false
          if commandName:lower():sub(1, #inputLower) == inputLower then
            matches = true
          else
            local aliases: { string } = commandModule.Aliases or {}
            for _, alias: string in ipairs(aliases) do
              if alias:lower():sub(1, #inputLower) == inputLower then
                matches = true
                break
              end
            end
          end

          if matches and table.find(commands :: Types.AccessibleCommands, commandName) then
            local baseUsage = commandModule.Usage or commandName .. " [args]"
            local prefix = Configuration.Commands.PREFIX or DEFAULT_PREFIX

            local commandInfo: Types.AvailableCommand = {
              name = commandName,
              description = commandModule.Description or "No description available",
              usage = prefix .. baseUsage,
              aliases = commandModule.Aliases or {},
            }
            table.insert(suggestions, commandInfo)
            if #suggestions >= maxSuggestions then
              break
            end
          end
        end
      end
    elseif #tokens >= 2 then
      -- Player name suggestions for specific commands
      local commandName = tokens[1]:lower()
      local isPlayerCommand = false
      local playerCommands = Configuration.Commands.PLAYER_ARGUMENT_COMMANDS
      if playerCommands and type(playerCommands) == "table" then
        for _, cmd in ipairs(playerCommands) do
          if commandName == cmd then
            isPlayerCommand = true
            break
          end
        end
      end

      if isPlayerCommand then
        local partialName = tokens[2]:lower()
        for _, otherPlayer in ipairs(Players:GetPlayers()) do
          if Utils.playerNameMatches(otherPlayer, partialName) then
            local commandModule = CommandDispatcher.getCommandModule(commandName)
            local prefix = Configuration.Commands.PREFIX or DEFAULT_PREFIX
            if commandModule and table.find(commands :: Types.AccessibleCommands, commandName) then
              local commandInfo: Types.AvailableCommand = {
                name = commandName,
                description = commandModule.Description or "No description available",
                usage = prefix .. commandName .. " " .. otherPlayer.Name,
                aliases = commandModule.Aliases or {},
              }
              table.insert(suggestions, commandInfo)
              if #suggestions >= maxSuggestions then
                break
              end
            end
          end
        end
      end
    end
  end

  table.sort(suggestions, function(a, b)
    return a.name < b.name
  end)

  Utils.print(
    MODULE_NAME,
    string.format("Returned %d autocomplete suggestions for %s", #suggestions, player.Name)
  )
  Utils.endTimer(MODULE_NAME, "HandleAutocompleteRequest")
  return { success = true, data = suggestions }
end

-- ============================================================================
-- PUBLIC FUNCTIONS
-- ============================================================================

-- Initialize the remotes system
local function init(): boolean
  if isInitialized then
    Utils.print(MODULE_NAME, "Remotes system already initialized")
    return true
  end

  Utils.startTimer(MODULE_NAME, "Init")

  local success: boolean, errorMessage: string? = pcall(function()
    local mcsFolder = ReplicatedStorage:FindFirstChild("MCS")
    if not mcsFolder then
      mcsFolder = Instance.new("Folder")
      mcsFolder.Name = "MCS"
      mcsFolder.Parent = ReplicatedStorage
    end
    assert(mcsFolder:IsA("Folder"), "MCS folder is not a Folder instance")

    remoteFolder = mcsFolder:FindFirstChild("Remotes")
    if not remoteFolder then
      remoteFolder = Instance.new("Folder")
      remoteFolder.Name = "Remotes"
      remoteFolder.Parent = mcsFolder
    end
    assert(remoteFolder:IsA("Folder"), "Remotes folder is not a Folder instance")

    commandRemote = Instance.new("RemoteEvent")
    commandRemote.Name = "CommandRemote"
    commandRemote.Parent = remoteFolder

    autocompleteRemote = Instance.new("RemoteFunction")
    autocompleteRemote.Name = "AutocompleteRemote"
    autocompleteRemote.Parent = remoteFolder

    checkConsolePermission = Instance.new("RemoteFunction")
    checkConsolePermission.Name = "CheckConsolePermission"
    checkConsolePermission.Parent = remoteFolder

    commandRemote.OnServerEvent:Connect(function(player: Player, request: Types.CommandRequest)
      local response = handleCommandRequest(player, request)
      if commandRemote then
        commandRemote:FireClient(player, response)
      else
        Utils.print(MODULE_NAME, "Warning: commandRemote is nil during FireClient")
      end
    end)

    autocompleteRemote.OnServerInvoke = function(player: Player, input: string): Types.QueryResponse
      return handleAutocompleteRequest(player, input)
    end

    checkConsolePermission.OnServerInvoke = function(player: Player): boolean
      local effectiveLevel = PermissionService.getEffectivePermissionLevel(player)
      return effectiveLevel >= PermissionService.Levels.DISDevelopers
    end

    isInitialized = true
    Utils.print(MODULE_NAME, "Remotes system initialized successfully")
  end)

  Utils.endTimer(MODULE_NAME, "Init")

  if not success then
    Utils.print(
      MODULE_NAME,
      string.format("Failed to initialize remotes: %s", errorMessage or "Unknown error")
    )
    return false
  end

  return true
end

-- Cleanup the remotes system
local function cleanup()
  Utils.print(MODULE_NAME, "Cleaning up remotes system")

  if remoteFolder then
    remoteFolder:Destroy()
    remoteFolder = nil
  end

  commandRemote = nil
  autocompleteRemote = nil
  checkConsolePermission = nil
  isInitialized = false

  Utils.print(MODULE_NAME, "Remotes system cleaned up")
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return {
  init = init,
  cleanup = cleanup,
}
